'use client';

import { useAuthStore } from '@/store/accountStore';
import { CreditDisplay } from '@/components/background-remover/components/CreditDisplay';

/**
 * 用于测试CreditDisplay组件的调试组件
 */
export function CreditDisplayTest() {
  const { 
    hasPerformedBackgroundRemoval, 
    lastDailyVisit, 
    markBackgroundRemovalPerformed,
    resetDailyState,
    checkAndUpdateDailyVisit 
  } = useAuthStore();

  return (
    <div className="p-6 space-y-4">
      <h2 className="text-xl font-bold">CreditDisplay 测试</h2>
      
      {/* 当前状态显示 */}
      <div className="bg-gray-100 p-4 rounded">
        <h3 className="font-semibold mb-2">当前状态:</h3>
        <p>已执行去背操作: {hasPerformedBackgroundRemoval ? '是' : '否'}</p>
        <p>最后访问日期: {lastDailyVisit || '未设置'}</p>
      </div>

      {/* CreditDisplay 组件 */}
      <div className="bg-white p-4 rounded border">
        <h3 className="font-semibold mb-2">CreditDisplay 组件:</h3>
        <CreditDisplay />
      </div>

      {/* 测试按钮 */}
      <div className="space-x-2">
        <button 
          onClick={markBackgroundRemovalPerformed}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          模拟去背操作
        </button>
        
        <button 
          onClick={resetDailyState}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          重置每日状态
        </button>
        
        <button 
          onClick={() => {
            const isFirstTime = checkAndUpdateDailyVisit();
            alert(`是否首次访问: ${isFirstTime ? '是' : '否'}`);
          }}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          检查每日访问
        </button>
      </div>
    </div>
  );
}
