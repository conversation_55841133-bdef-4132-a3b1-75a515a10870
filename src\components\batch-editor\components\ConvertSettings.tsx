'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import Image from 'next/image';
import { shouldDisableBatchApplyButton } from '../../../lib/buttonUtils';

interface ConvertSettingsProps {
  onApply: (settings: ConvertSettings) => void;
  images: Array<{
    id: string;
    status: string;
    originalFormat?: string;
  }>;
  isProcessing?: boolean;
}

interface ConvertSettings {
  format: string;
}

type ImageFormat = 'original' | 'png' | 'jpg' | 'webp' | 'bmp' | 'xbm' | 'xpm';

const FORMAT_OPTIONS: Array<{
  value: ImageFormat;
  label: string;
}> = [
  {
    value: 'original',
    label: 'Original',
  },
  { value: 'png', label: 'PNG' },
  { value: 'jpg', label: 'JPG' },
  { value: 'webp', label: 'WEBP' },
  { value: 'bmp', label: 'BMP' },
  { value: 'xbm', label: 'XBM' },
  { value: 'xpm', label: 'XPM' },
];

/**
 * 批量格式转换设置组件
 * 提供图片格式转换功能，支持恢复原始格式
 */
export function ConvertSettings({
  onApply,
  images,
  isProcessing = false,
}: ConvertSettingsProps) {
  const [selectedFormat, setSelectedFormat] = useState<ImageFormat>('original');

  // 应用设置
  const handleApply = () => {
    const settings: ConvertSettings = {
      format: selectedFormat,
    };
    onApply(settings);
  };

  return (
    <div className='w-[344px] bg-white border-border border-r flex flex-col h-full'>
      {/* 标题区域 */}
      <div className='pt-6 px-4'>
        <div className='border-b border-border pb-4'>
          <h2 className='text-base font-bold'>Convert</h2>
        </div>
      </div>

      {/* 设置区域 */}
      <div className='flex-1 px-4 pt-4 space-y-4'>
        {/* 格式选择 */}
        <div className='space-y-2'>
          {FORMAT_OPTIONS.map(option => (
            <div
              key={option.value}
              onClick={() => setSelectedFormat(option.value)}
              className={`
                  relative flex h-10 w-full cursor-pointer items-center rounded-lg py-2 px-3 text-sm outline-none select-none
                  hover:bg-[#F0F0F0] transition-colors
                  ${
                    selectedFormat === option.value
                      ? 'bg-[rgba(255,204,3,0.3)] text-black'
                      : 'text-black'
                  }
                `}
            >
              <span className='flex size-4 items-center justify-center mr-3'>
                {selectedFormat === option.value && (
                  <Image
                    src='/apps/icons/check.svg'
                    alt='check'
                    width={16}
                    height={16}
                  />
                )}
              </span>
              <div className='flex flex-col'>
                <span className='text-base'>{option.label}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 底部应用按钮 */}
      <div className='px-4 py-6 bg-white rounded-b-2xl'>
        <Button
          onClick={handleApply}
          disabled={shouldDisableBatchApplyButton(isProcessing, images)}
          className='w-full h-12 bg-[#FFCC03] hover:bg-[#FFCC03]/90 text-text-primary text-base font-medium rounded-xl'
        >
          {isProcessing ? (
            <div className='flex items-center gap-2'>
              <Image
                src='/icons/lock.svg'
                alt='processing'
                width={16}
                height={16}
                className='animate-spin'
              />
              Processing...
            </div>
          ) : (
            'Apply'
          )}
        </Button>
      </div>
    </div>
  );
}
