'use client';

import { useState, useEffect } from 'react';
import { X } from 'lucide-react';

/**
 * 首次访问欢迎提示组件
 * 显示用户获得免费积分的提示信息
 */
export function FirstTimeWelcome() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // 检查是否是首次访问
    const hasSeenWelcome = localStorage.getItem('hasSeenWelcome');
    
    if (!hasSeenWelcome) {
      setIsVisible(true);
      // 标记用户已经看过欢迎提示
      localStorage.setItem('hasSeenWelcome', 'true');
    }
  }, []);

  const handleClose = () => {
    setIsVisible(false);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className='fixed top-4 left-1/2 transform -translate-x-1/2 z-50'>
      <div className='bg-white border border-[#e7e7e7] rounded-lg shadow-lg px-4 py-3 flex items-center gap-3 min-w-[320px]'>
        {/* 礼物图标 */}
        <div className='w-8 h-8 bg-gradient-to-br from-[#FFBB3E] to-[#FF781F] rounded-full flex items-center justify-center flex-shrink-0'>
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path 
              d="M8 2L9.2 5.8L13 7L9.2 8.2L8 12L6.8 8.2L3 7L6.8 5.8L8 2Z" 
              fill="white"
            />
          </svg>
        </div>
        
        {/* 提示文本 */}
        <div className='flex-1'>
          <p className='text-[#121212] text-sm font-medium'>
            You've received 5 free AI credits today
          </p>
        </div>
        
        {/* 关闭按钮 */}
        <button
          onClick={handleClose}
          className='w-6 h-6 flex items-center justify-center text-[#878787] hover:text-[#121212] transition-colors'
        >
          <X size={14} />
        </button>
      </div>
    </div>
  );
}
