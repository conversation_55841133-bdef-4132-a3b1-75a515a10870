'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react';
import Image from 'next/image';
import { shouldDisableBatchApplyButton } from '../../../lib/buttonUtils';

interface RenameSettingsProps {
  onApply: (settings: RenameSettings) => void;
  images: Array<{
    id: string;
    status: string;
  }>;
  isProcessing?: boolean;
}

interface RenameSettings {
  prefix: string;
  startNumber: number;
  numberStep: number;
}

/**
 * 批量重命名设置组件
 * 提供名称前缀、起始序号、序号间隔的设置功能
 */
export function RenameSettings({
  onApply,
  images,
  isProcessing = false,
}: RenameSettingsProps) {
  const [prefix, setPrefix] = useState('');
  const [startNumber, setStartNumber] = useState(1);
  const [numberStep, setNumberStep] = useState(1);

  // 处理数字输入的增减
  const handleNumberChange = (
    type: 'startNumber' | 'numberStep',
    operation: 'increment' | 'decrement'
  ) => {
    if (type === 'startNumber') {
      const newValue =
        operation === 'increment' ? startNumber + 1 : startNumber - 1;
      setStartNumber(Math.max(1, newValue)); // 最小值为1
    } else {
      const newValue =
        operation === 'increment' ? numberStep + 1 : numberStep - 1;
      setNumberStep(Math.max(1, newValue)); // 最小值为1
    }
  };

  // 处理数字输入框的直接输入
  const handleNumberInputChange = (
    type: 'startNumber' | 'numberStep',
    value: string
  ) => {
    const numValue = parseInt(value) || 1;
    if (type === 'startNumber') {
      setStartNumber(Math.max(1, numValue));
    } else {
      setNumberStep(Math.max(1, numValue));
    }
  };

  // 应用设置
  const handleApply = () => {
    const settings: RenameSettings = {
      prefix: prefix.trim(),
      startNumber,
      numberStep,
    };
    onApply(settings);
  };

  return (
    <div className='w-[344px] bg-white border-border border-r flex flex-col h-full'>
      {/* 标题区域 */}
      <div className='pt-6 px-4'>
        <div className='border-b border-border pb-4'>
          <h2 className='text-base font-bold'>Rename</h2>
        </div>
      </div>

      {/* 设置区域 */}
      <div className='flex-1 px-4 pt-4 space-y-4'>
        {/* 名称前缀 */}
        <div className='space-y-3'>
          <label className='block text-[#878787] text-sm font-medium'>
            Prefix
          </label>
          <div className='space-y-2'>
            <Input
              placeholder='Please enter a name'
              value={prefix}
              onChange={e => setPrefix(e.target.value)}
              className='h-10 bg-[#f9fafb] border border-[#e7e7e7] rounded-lg px-3 py-[9px] text-[14px] placeholder:text-[#b8b8b8] focus:border-[#FFCC03] transition-colors'
            />
          </div>
        </div>

        {/* 起始序号 */}
        <div className='space-y-3'>
          <label className='block text-[#878787] text-sm font-medium'>
            Start Number
          </label>
          <div className='space-y-2'>
            <div className='relative'>
              <div className='h-10 bg-[#f9fafb] flex items-center'>
                {/* 减少按钮 */}
                <button
                  type='button'
                  onClick={() => handleNumberChange('startNumber', 'decrement')}
                  className='w-10 h-10 flex items-center justify-center bg-[#f9fafb] rounded-l-lg border-l border-t border-b border-[#e7e7e7] transition-colors'
                  disabled={startNumber <= 1}
                >
                  <div className='w-6 h-6 flex items-center justify-center rounded hover:bg-[#f0f0f0] transition-colors'>
                    <ChevronLeftIcon
                      className={`w-4 h-4 text-text-primary transition-opacity ${startNumber <= 1 ? 'opacity-30' : 'opacity-100'}`}
                    />
                  </div>
                </button>

                {/* 数字输入框 */}
                <div className='flex-1 px-3 flex items-center justify-center h-full border border-[#e7e7e7] focus-within:border-[#FFCC03] transition-all'>
                  <Input
                    type='number'
                    value={startNumber}
                    onChange={e =>
                      handleNumberInputChange('startNumber', e.target.value)
                    }
                    className='w-full text-center bg-transparent border-0 p-0 h-auto text-[14px] text-text-primary shadow-none'
                    min={1}
                  />
                </div>

                {/* 增加按钮 */}
                <button
                  type='button'
                  onClick={() => handleNumberChange('startNumber', 'increment')}
                  className='w-10 h-10 flex items-center justify-center bg-[#f9fafb] rounded-r-lg border-r border-t border-b border-[#e7e7e7] transition-colors'
                >
                  <div className='w-6 h-6 flex items-center justify-center rounded hover:bg-[#f0f0f0] transition-colors'>
                    <ChevronRightIcon className='w-4 h-4 text-text-primary' />
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 序号间隔 */}
        <div className='space-y-3'>
          <label className='block text-[#878787] text-sm font-medium'>
            Number Step
          </label>
          <div className='space-y-2'>
            <div className='relative'>
              <div className='h-10 bg-[#f9fafb] flex items-center'>
                {/* 减少按钮 */}
                <button
                  type='button'
                  onClick={() => handleNumberChange('numberStep', 'decrement')}
                  className='w-10 h-10 flex items-center justify-center bg-[#f9fafb] rounded-l-lg border-l border-t border-b border-[#e7e7e7] transition-colors'
                  disabled={numberStep <= 1}
                >
                  <div className='w-6 h-6 flex items-center justify-center rounded hover:bg-[#f0f0f0] transition-colors'>
                    <ChevronLeftIcon
                      className={`w-4 h-4 text-text-primary transition-opacity ${numberStep <= 1 ? 'opacity-30' : 'opacity-100'}`}
                    />
                  </div>
                </button>

                {/* 数字输入框 */}
                <div className='flex-1 px-3 flex items-center justify-center h-full border border-[#e7e7e7] focus-within:border-[#FFCC03] transition-all'>
                  {' '}
                  <Input
                    type='number'
                    value={numberStep}
                    onChange={e =>
                      handleNumberInputChange('numberStep', e.target.value)
                    }
                    className='w-full text-center bg-transparent border-0 p-0 h-auto text-[14px] text-text-primary shadow-none'
                    min={1}
                  />
                </div>

                {/* 增加按钮 */}
                <button
                  type='button'
                  onClick={() => handleNumberChange('numberStep', 'increment')}
                  className='w-10 h-10 flex items-center justify-center bg-[#f9fafb] rounded-r-lg border-r border-t border-b border-[#e7e7e7] transition-colors'
                >
                  <div className='w-6 h-6 flex items-center justify-center rounded hover:bg-[#f0f0f0] transition-colors'>
                    <ChevronRightIcon className='w-4 h-4 text-text-primary' />
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 底部应用按钮 */}
      <div className='px-4 py-6 bg-white rounded-b-2xl'>
        <Button
          onClick={handleApply}
          disabled={
            shouldDisableBatchApplyButton(isProcessing, images) ||
            !prefix.trim()
          }
          className='w-full h-12 bg-[#FFCC03] hover:bg-[#FFCC03]/90 text-text-primary text-base font-medium rounded-xl'
        >
          {isProcessing ? (
            <div className='flex items-center gap-2'>
              <Image
                src='/apps/icons/loading.png'
                alt='loading'
                width={16}
                height={16}
                className='animate-spin'
              />
              Processing...
            </div>
          ) : (
            'Apply'
          )}
        </Button>
      </div>
    </div>
  );
}
