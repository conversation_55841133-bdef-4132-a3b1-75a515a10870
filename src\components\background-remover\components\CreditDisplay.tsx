'use client';

import { useEffect } from 'react';
import { useAuthStore } from '@/store/accountStore';

/**
 * 积分消耗显示组件
 * 显示当前用户的积分使用情况，UI参考Figma设计
 * 支持首次访问欢迎消息和积分使用状态切换
 */
export function CreditDisplay() {
  const { userInfo, hasPerformedBackgroundRemoval, checkAndUpdateDailyVisit } =
    useAuthStore();

  // 检查是否是今日首次访问
  useEffect(() => {
    checkAndUpdateDailyVisit();
  }, [checkAndUpdateDailyVisit]);

  // 从用户信息中获取积分数据
  const currentScore = userInfo?.score ?? 0;
  const totalScore = userInfo?.total_score ? parseInt(userInfo.total_score) : 0;
  const usedCredits = totalScore - currentScore;

  // 确定显示的文本内容
  const isFirstTimeToday = !hasPerformedBackgroundRemoval;
  const displayText = isFirstTimeToday
    ? "You've received 5 free AI credits today"
    : `${usedCredits}/${totalScore} free credits used`;

  return (
    <div className='bg-[rgba(255,204,3,0.1)] rounded-[100px] px-[10px] py-[10px] flex items-center justify-center gap-[10px] w-[293px] h-[40px]'>
      <div className='flex items-center gap-[8px]'>
        {/* AI 图标 */}
        <div className='w-[24px] h-[24px] bg-white rounded flex items-center justify-center relative'>
          {/* AI 图标的渐变背景 */}
          <div className='w-[20.5px] h-[20.5px] relative'>
            {/* 主圆形背景 */}
            <div
              className='absolute inset-0 rounded-full'
              style={{
                background:
                  'linear-gradient(180deg, #FFDF60 0%, #FFA825 82.42%, #FF8927 100%)',
              }}
            />
            {/* 内部小圆 */}
            <div
              className='absolute w-[4.5px] h-[4.5px] bg-[#FFDF60] rounded-full'
              style={{
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
              }}
            />
            {/* 波浪形状 */}
            <svg
              className='absolute'
              style={{
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
              }}
              width='16'
              height='13'
              viewBox='0 0 16 13'
              fill='none'
            >
              <path
                d='M2.5 6.5C2.5 6.5 4.5 2.5 8 6.5C11.5 10.5 13.5 6.5 13.5 6.5'
                stroke='#FFDF60'
                strokeWidth='1.5'
                strokeLinecap='round'
              />
            </svg>
          </div>

          {/* 右上角星星 */}
          <div
            className='absolute w-[11px] h-[11px]'
            style={{
              top: '0px',
              right: '-13px',
            }}
          >
            <svg width='11' height='11' viewBox='0 0 11 11' fill='none'>
              <path
                d='M5.5 0L6.7 3.8L11 5.5L6.7 7.2L5.5 11L4.3 7.2L0 5.5L4.3 3.8L5.5 0Z'
                fill='url(#starGradient1)'
              />
              <defs>
                <linearGradient
                  id='starGradient1'
                  x1='5.5'
                  y1='0'
                  x2='5.5'
                  y2='11'
                  gradientUnits='userSpaceOnUse'
                >
                  <stop stopColor='#FFBB3E' />
                  <stop offset='1' stopColor='#FF781F' />
                </linearGradient>
              </defs>
            </svg>
          </div>

          {/* 右上角小星星 */}
          <div
            className='absolute w-[4.2px] h-[4.2px] opacity-40'
            style={{
              top: '0px',
              right: '-20px',
            }}
          >
            <svg width='4.2' height='4.2' viewBox='0 0 4.2 4.2' fill='none'>
              <path
                d='M2.1 0L2.5 1.4L4.2 2.1L2.5 2.8L2.1 4.2L1.7 2.8L0 2.1L1.7 1.4L2.1 0Z'
                fill='url(#starGradient2)'
              />
              <defs>
                <linearGradient
                  id='starGradient2'
                  x1='2.1'
                  y1='0'
                  x2='2.1'
                  y2='4.2'
                  gradientUnits='userSpaceOnUse'
                >
                  <stop stopColor='#FFBB3E' />
                  <stop offset='1' stopColor='#FF781F' />
                </linearGradient>
              </defs>
            </svg>
          </div>
        </div>

        {/* 文本内容 */}
        <div className='flex items-center gap-[8px]'>
          <span className='text-[#121212] text-[14px] font-normal leading-[1.5em]'>
            Advanced AI
          </span>

          {/* 分隔线 */}
          <div className='w-[1px] h-[12px] bg-[#D0D0D0]' />

          <span className='text-[#121212] text-[14px] font-normal leading-[1.5em]'>
            {displayText}
          </span>
        </div>
      </div>
    </div>
  );
}
